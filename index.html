<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Portfolio | Full Stack Web Developer & Content Writer</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header Section -->
    <header>
        <div class="logo">
            <h1 class="name-logo"><PERSON><PERSON></h1>
        </div>
        <nav>
            <ul>
                <li><a href="#home" class="active">Home</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#skills">Skills</a></li>
                <li><a href="#projects">Projects</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </nav>
        <div class="menu-toggle">
            <i class="fas fa-bars"></i>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero" id="home">
        <!-- Full-screen background image -->
        <div class="hero-image-container">
            <img src="images/myimage.png" alt="Sruti Gupta" class="hero-bg-image">
        </div>

        <!-- Text overlay -->
        <div class="hero-overlay">
            <div class="hero-text">
                <h1 class="shimmery-text">Sruti Gupta</h1>
                <h2>Full Stack Web Developer & Content Writer</h2>
                <div class="hero-buttons">
                    <a href="#about" class="btn primary-btn">About Me</a>
                    <a href="#projects" class="btn secondary-btn">My Work</a>
                </div>
            </div>
        </div>

        <!-- Scroll indicator -->
        <div class="scroll-indicator">
            <a href="#about">
                <i class="fas fa-chevron-down"></i>
            </a>
        </div>
    </section>

    <!-- About Section -->
    <section class="about" id="about">
        <div class="section-title fade-in-up">
            <h2>ABOUT ME</h2>
        </div>
        <div class="about-content">
            <div class="about-image fade-in-left">
                <!-- About image -->
                <img src="images/sruti.jpg" alt="About Me">
            </div>
            <div class="about-text fade-in-right">
               <p>I'm Sruti Gupta, a passionate Full-Stack Developer with a strong foundation in backend development. With solid expertise in Data Structures, OOPs, and core computer science concepts like Operating Systems and DBMS, I specialize in building scalable, efficient, and seamless digital solutions.
    <br><br>I enjoy tackling challenging problems, designing robust backend architectures, and creating full-fledged web applications that are both functional and high-performing. My journey has evolved from UI/UX design to a deep focus on development, allowing me to combine creativity with technical precision.
    <br><br>Alongside coding, my experience in content writing strengthens my communication skills, enabling me to explain complex technical concepts clearly through documentation, blogs, or project reports.
    <br><br>Currently pursuing my B.E. in Information Technology at UIT, Burdwan University, I’m eager to keep learning, experimenting, and contributing to meaningful projects that push the boundaries of technology.</p>

                <div class="personal-info">
                    <div class="info-item">
                        <span>Date of Birth:</span>
                        <span>20/01/2005</span>
                    </div>
                    <div class="info-item">
                        <span>Language:</span>
                        <span>English,Hindi,Bengali</span>
                    </div>
                    <div class="info-item">
                        <span>Email:</span>
                        <span><EMAIL></span>
                    </div>
                    <div class="info-item">
                        <span>Country:</span>
                        <span>India</span>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- Skills Section -->
    <section class="skills" id="skills">
        <div class="section-title fade-in-up">
            <h2>MY SKILLS</h2>
        </div>
        <div class="skills-container">
            <div class="skills-category fade-in-up">
                <div class="skills-category-title">
                    <i class="fas fa-code"></i>
                    <h3>Languages</h3>
                </div>
                <div class="skills-list">
                    <div class="skill-item">
                        <span>Python</span>
                    </div>
                    <div class="skill-item">
                        <span>C</span>
                    </div>
                    <div class="skill-item">
                        <span>C++</span>
                    </div>
                    <div class="skill-item">
                        <span>JAVA</span>
                    </div>
                </div>
            </div>

            <div class="skills-category fade-in-up">
                <div class="skills-category-title">
                    <i class="fas fa-laptop-code"></i>
                    <h3>Technologies & Frameworks</h3>
                </div>
                <div class="skills-list">
                    <div class="skill-item">
                        <span>HTML5/CSS3</span>
                    </div>
                    <div class="skill-item">
                        <span>JavaScript</span>
                    </div>
                    <div class="skill-item">
                        <span>React</span>
                    </div>
                    <div class="skill-item">
                        <span>Node.js</span>
                    </div>
                    <div class="skill-item">
                        <span>TypeScript</span>
                    </div>
                    <div class="skill-item">
                        <span>Tailwind CSS</span>
                    </div>
                </div>
            </div>

            <div class="skills-category fade-in-up">
                <div class="skills-category-title">
                    <i class="fas fa-cubes"></i>
                    <h3>Blockchain & Web3</h3>
                </div>
                <div class="skills-list">
                    <div class="skill-item">
                        <span>Solidity</span>
                    </div>
                </div>
            </div>

            <div class="skills-category fade-in-up">
                <div class="skills-category-title">
                    <i class="fas fa-pen-fancy"></i>
                    <h3>Content Writing</h3>
                </div>
                <div class="skills-list">
                    <div class="skill-item">
                        <span>Technical Writing</span>
                    </div>
                    <div class="skill-item">
                        <span>Blog Writing</span>
                    </div>
                    <div class="skill-item">
                        <span>Copywriting</span>
                    </div>
                    <div class="skill-item">
                        <span>Social Media Writing</span>
                    </div>
                    <div class="skill-item">
                        <span>SEO Content</span>
                    </div>
                    <div class="skill-item">
                        <span>Creative Writing</span>
                    </div>
                </div>
            </div>

            <div class="skills-category fade-in-up">
                <div class="skills-category-title">
                    <i class="fas fa-tools"></i>
                    <h3>Tools</h3>
                </div>
                <div class="skills-list">
                    <div class="skill-item">
                        <span>VS Code</span>
                    </div>
                    <div class="skill-item">
                        <span>GitHub</span>
                    </div>
                    <div class="skill-item">
                        <span>Figma</span>
                    </div>
                    <div class="skill-item">
                        <span>Docker</span>
                    </div>
                    <div class="skill-item">
                        <span>Canva</span>
                    </div>
                    <div class="skill-item">
                        <span>Bitbucket</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Projects Section -->
    <section class="projects" id="projects">
        <div class="section-title fade-in-up">
            <h2>MY PROJECTS</h2>
        </div>
        <div class="projects-content">
            <div class="project-item fade-in-up">
                <div class="project-number"></div>
                <div class="project-info">
                    <div class="project-title">
                        <h3>Finani – Your Personal Finance Dashboard</h3>
                        <div class="project-tech-stack">
                            <span class="tech-tag">Next.js</span>
                            <span class="tech-tag">React</span>
                            <span class="tech-tag">Tailwind CSS</span>
                            <span class="tech-tag">JavaScript</span>
                        </div>
                    </div>
                    <p>A smart, minimalistic finance tracker that simplifies money management for everyday users. Built with a mobile-first approach, Finani helps users log expenses and income, auto-calculates balance, and offers a responsive dashboard that adapts across devices. With dark/light mode toggle, real-time updates, and localStorage support, Finani turns spreadsheets into an intuitive, user-friendly experience.</p>

                    <div class="project-features">
                        <h4>Key Features:</h4>
                        <ul>
                            <li>Expense & income tracking with live balance updates</li>
                            <li>Clean UI with dark/light theme switch</li>
                            <li>Fully responsive and mobile-friendly</li>
                            <li>Persistent data via localStorage</li>
                            <li>Built using Next.js for fast rendering</li>
                        </ul>
                    </div>

                    <div class="project-links">
                        <a href="https://finani.vercel.app/" target="_blank" class="project-link"><i class="fas fa-external-link-alt"></i> Live Demo</a>
                    </div>
                </div>
                <div class="project-image">
                    <img src="images/finani.png" alt="Finani Finance Dashboard">
                </div>
            </div>

            <div class="project-item fade-in-up">
                <div class="project-number"></div>
                <div class="project-info">
                    <div class="project-title">
                        <h3>Invoice App – Smart Invoice Generator</h3>
                        <div class="project-tech-stack">
                            <span class="tech-tag">JavaScript</span>
                            <span class="tech-tag">Tailwind CSS</span>
                            <span class="tech-tag">HTML</span>
                            <span class="tech-tag">Responsive Design</span>
                        </div>
                    </div>
                    <p>An intuitive invoicing tool designed for freelancers and small businesses to create professional invoices with ease. This web app features real-time tax, discount, and delivery fee calculations, along with a sleek UI and light/dark mode support. It includes PDF export functionality and currency switching, making it perfect for global clients. LocalStorage ensures that data persists across sessions for a seamless experience.</p>

                    <div class="project-features">
                        <h4>Key Features:</h4>
                        <ul>
                            <li>Auto-calculated taxes, discounts, and delivery fees</li>
                            <li>Currency switcher for international use</li>
                            <li>PDF download for professional invoices</li>
                            <li>Dark/light mode toggle for visual comfort</li>
                            <li>LocalStorage for data retention across sessions</li>
                            <li>Fully responsive and mobile-ready</li>
                        </ul>
                    </div>

                    <div class="project-links">
                        <a href="https://myinvoice-app.netlify.app/" target="_blank" class="project-link"><i class="fas fa-external-link-alt"></i> Live Demo</a>
                    </div>
                </div>
                <div class="project-image">
                    <img src="images/invoice.jpeg" alt="Invoice App Generator">
                </div>
            </div>

            <div class="project-item fade-in-up">
                <div class="project-number"></div>
                <div class="project-info">
                    <div class="project-title">
                        <h3>Portfolio Website – Personal Developer Showcase</h3>
                        <div class="project-tech-stack">
                            <span class="tech-tag">HTML5</span>
                            <span class="tech-tag">CSS3</span>
                            <span class="tech-tag">JavaScript</span>
                            <span class="tech-tag">Responsive Design</span>
                        </div>
                    </div>
                    <p>The UI/UX is designed for fluid, intuitive interactions, ensuring an immersive gaming experience.<br>A modern, responsive portfolio website designed to showcase my skills, projects, and experience as a Web Developer and UI/UX Designer. The site features a clean, dark-themed design with subtle animations and a user-friendly interface. The portfolio is optimized for all devices and includes sections for skills, projects, and contact information.</p>

                    <div class="project-features">
                        <h4>Key Features:</h4>
                        <ul>
                            <li>Responsive design that works seamlessly across all devices</li>
                            <li>Subtle animations and transitions for enhanced user experience</li>
                            <li>Dark theme with blue accent colors for modern aesthetics</li>
                            <li>Optimized performance with minimal load times</li>
                            <li>Clean, organized codebase for easy maintenance</li>
                        </ul>
                    </div>

                    <div class="project-links">
                        <a href="#" class="project-link"><i class="fas fa-external-link-alt"></i> You're viewing it now!</a>
                    </div>
                </div>
                <div class="project-image">
                    <img src="images/port.jpeg" alt="Portfolio Website">
                </div>
            </div>

            <div class="project-item fade-in-up">
                <div class="project-number"></div>
                <div class="project-info">
                    <div class="project-title">
                        <h3>Monad Chain Game – A Revolutionary NFT-Based Card Game</h3>
                        <div class="project-tech-stack">
                            <span class="tech-tag">Tailwind CSS</span>
                            <span class="tech-tag">React</span>
                            <span class="tech-tag">Solidity</span>
                            <span class="tech-tag">Blockchain</span>
                            <span class="tech-tag">Monad</span>
                            <span class="tech-tag">NFT Composability</span>
                        </div>
                    </div>
                    <p>An exciting and fast-paced card game built with blockchain technology, where players battle with NFTs in real-time. The UI/UX is designed for fluid, intuitive interactions, ensuring an immersive gaming experience. The clean, modern interface adapts seamlessly to various devices, and the game mechanics are optimized for quick decision-making and strategy.</p>

                    <div class="project-features">
                        <h4>Key Features:</h4>
                        <ul>
                            <li>Parallel Execution: Multiple actions (like attack + heal) happen simultaneously for faster gameplay</li>
                            <li>On-Chain Logic: Card effects, rewards, and shuffling are 100% transparent and decentralized</li>
                            <li>NFT Integration: Burn-to-evolve cards, allowing players to merge and enhance their NFTs</li>
                            <li>Real-Time Combat: Engage in dynamic PvP or AI battles with instant, on-chain results</li>
                            <li>Mobile-First Design: Optimized for responsiveness, providing a great experience across all devices</li>
                        </ul>
                    </div>

                    <div class="project-links">
                        <a href="#" class="project-link disabled"><i class="fas fa-lock"></i> Coming Soon</a>
                    </div>
                </div>
                <div class="project-image">
                    <img src="images/monad-game.jpg" alt="Monad Chain Game">
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="contact" id="contact">
        <div class="contact-container">
            <div class="contact-info fade-in-left">
                <div class="section-title left-aligned">
                    <h2>Get in Touch</h2>
                </div>
                <p class="contact-description">Whether you're looking for a skilled Full-Stack Developer or a creative collaborator to bring your ideas to life—I'm open to exciting opportunities. Let’s connect and build something great together!</p>

                <div class="contact-details">
                    <div class="contact-item animate-item">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="contact-text">
                            <h3>Email</h3>
                            <p><EMAIL></p>
                        </div>
                    </div>

                    <div class="contact-item animate-item">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div class="contact-text">
                            <h3>Location</h3>
                            <p>East Burdwan, India</p>
                        </div>
                    </div>
                </div>

                <div class="contact-social">
                    <h3>Follow Me</h3>
                    <div class="social-icons">
                        <a href="https://github.com/SrutiGupta" class="social-icon"><i class="fab fa-github"></i></a>
                        <a href="https://www.linkedin.com/in/sruti-gupta-2979032a2/" class="social-icon"><i class="fab fa-linkedin"></i></a>
                        <a href="https://www.instagram.com/_miss__sruti/" class="social-icon"><i class="fab fa-instagram"></i></a>
                    </div>
                </div>
            </div>

            <div class="contact-form-container fade-in-right">
                <form id="contact-form">
                    <h3>Send Me a Message</h3>
                    <div class="form-row">
                        <div class="form-group animate-form-item">
                            <label for="name">Name</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        <div class="form-group animate-form-item">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                    </div>
                    <div class="form-group animate-form-item">
                        <label for="subject">Subject</label>
                        <input type="text" id="subject" name="subject" required>
                    </div>
                    <div class="form-group animate-form-item">
                        <label for="message">Message</label>
                        <textarea id="message" name="message" required></textarea>
                    </div>
                    <button type="submit" class="btn primary-btn pulse-animation">
                        <i class="fas fa-paper-plane"></i> Send Message
                    </button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="footer-content">
            <div class="footer-left">
                <div class="footer-branding">
                    <h3 class="footer-name">Sruti Gupta<span>.</span></h3>
                    <p class="footer-tagline">Full stack Web Developer & Content Writer</p>
                </div>
            </div>

            <div class="footer-code-snippet">
                <pre><code><span class="code-keyword">while</span>(<span class="code-variable">alive</span>) {
  <span class="code-function">eat</span>();
  <span class="code-function">sleep</span>();
  <span class="code-function">code</span>();
  <span class="code-function">repeat</span>();
}</code></pre>
            </div>

            <div class="footer-section">
                <h4>Quick Links</h4>
                <ul class="footer-links">
                    <li><a href="#home">Home</a></li>
                    <li><a href="#about">About</a></li>
                    <li><a href="#skills">Skills</a></li>
                    <li><a href="#projects">Projects</a></li>
                    <li><a href="#contact">Contact</a></li>
                </ul>
            </div>

            <div class="footer-section">
                <h4>Connect With Me</h4>
                <div class="social-icons">
                    <a href="https://github.com/SrutiGupta" aria-label="GitHub"><i class="fab fa-github"></i></a>
                    <a href="https://www.linkedin.com/in/sruti-gupta-2979032a2/" aria-label="LinkedIn"><i class="fab fa-linkedin"></i></a>
                    <a href="https://www.instagram.com/_miss__sruti/" aria-label="Instagram"><i class="fab fa-instagram"></i></a>
                </div>
            </div>
        </div>

        <div class="footer-bottom">
            <p>© <span id="year"></span> Sruti Gupta. All rights reserved.</p>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>