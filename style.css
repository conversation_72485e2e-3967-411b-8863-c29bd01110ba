/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #e0e0e0;
    background: #121212; /* Dark background */
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at top right, rgba(30, 136, 229, 0.1), transparent 60%);
    z-index: -1;
}

a {
    text-decoration: none;
    color: #333;
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
    height: auto;
    display: block;
}

.section-title {
    text-align: center;
    margin-bottom: 50px;
    color: #e0e0e0;
}

.section-title h2 {
    font-size: 2.5rem;
    font-weight: 700;
    position: relative;
    display: inline-block;
    padding-bottom: 10px;
}

.section-title h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 70%;
    height: 3px;
    background: linear-gradient(to right, #6a11cb, #2575fc);
}

.btn {
    display: inline-block;
    padding: 12px 30px;
    background: linear-gradient(to right, #6a11cb, #2575fc);
    color: #fff;
    border-radius: 30px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

section {
    padding: 100px 0;
}

/* Header Styles */
header {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    padding: 25px 10%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    z-index: 1000;
    background-color: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

header.scrolled {
    background-color: rgba(18, 18, 18, 0.95);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.3);
    padding: 20px 10%;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo h1 {
    font-size: 2rem;
    font-weight: 700;
    background: linear-gradient(to right, #1e88e5, #64b5f6);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
    letter-spacing: 1px;
    text-transform: uppercase;
}

.name-logo {
    font-size: 2.2rem !important;
    font-weight: 700 !important;
    font-style: italic;
    background: linear-gradient(to right, #1e88e5, #64b5f6, #90caf9) !important;
    background-size: 200% auto !important;
    -webkit-background-clip: text !important;
    background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    color: transparent !important;
    letter-spacing: 1.5px !important;
    text-transform: none !important;
    position: relative;
    padding-bottom: 3px;
    cursor: default;
    animation: shimmer 3s infinite linear;
}

@keyframes shimmer {
    0% {
        background-position: -100% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

.name-logo::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, #1e88e5, transparent);
    border-radius: 2px;
}

nav ul {
    display: flex;
    align-items: center;
}

nav ul li {
    margin: 0 25px;
    position: relative;
}

nav ul li a {
    font-weight: 600;
    position: relative;
    padding: 8px 0;
    color: white;
    transition: all 0.3s ease;
    font-size: 1.05rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
}

header.scrolled nav ul li a {
    color: #e0e0e0;
}

nav ul li a::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 0;
    height: 3px;
    background: linear-gradient(to right, #6a11cb, #2575fc);
    transition: width 0.3s ease;
    border-radius: 3px;
}

nav ul li a:hover::after,
nav ul li a.active::after {
    width: 100%;
}

nav ul li a:hover {
    transform: translateY(-3px);
}

/* Menu Toggle */
.menu-toggle {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    height: 100vh;
    width: 100%;
    position: relative;
    overflow: hidden;
    padding: 0;
    margin: 0;
}

/* Full-screen background image */
.hero-image-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.hero-bg-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: 85% center;
    display: block;
    animation: subtle-zoom 20s infinite alternate;
}

@keyframes subtle-zoom {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.05);
    }
}

/* Text overlay */
.hero-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.4) 30%, rgba(0, 0, 0, 0.1) 60%, rgba(0, 0, 0, 0) 100%);
    display: flex;
    align-items: flex-end;
    justify-content: flex-start;
    padding: 0 10%;
    box-sizing: border-box;
    z-index: 2;
}

.hero-text {
    color: #fff;
    max-width: 600px;
    padding-bottom: 15%;
    animation: fade-in 1.5s ease-out;
}

@keyframes fade-in {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.hero-text h1 {
    font-size: 4.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    line-height: 1.1;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.shimmery-text {
    color: #ffffff;
    position: relative;
    display: inline-block;
    letter-spacing: 1px;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.shimmery-text::after {
    content: '';
    position: absolute;
    bottom: -1px;
    left: 0;
    width: 100%;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
    background-size: 200% 100%;
    animation: shimmer-line 3s infinite linear;
}

@keyframes shimmer-line {
    0% {
        background-position: -100% 0;
    }
    100% {
        background-position: 100% 0;
    }
}

.shimmery-text:hover::after {
    animation-duration: 1.5s;
    height: 2px;
}

.hero-text h2 {
    font-size: 2rem;
    font-weight: 400;
    margin-bottom: 30px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

.hero-buttons {
    display: flex;
    gap: 20px;
    animation: fade-in 2s ease-out 0.5s both;
}

.primary-btn {
    background: linear-gradient(to right, #1e88e5, #64b5f6); /* Stronger Blue to Light Blue */
    color: white;
    padding: 15px 35px;
    border-radius: 30px;
    font-weight: 600;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 1rem;
    box-shadow: 0 4px 15px rgba(30, 136, 229, 0.3);
}

.primary-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.secondary-btn {
    background: transparent;
    color: white;
    border: 2px solid white;
    padding: 12px 30px;
    border-radius: 30px;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
    text-transform: uppercase;
    letter-spacing: 1px;
    font-size: 0.9rem;
}

.secondary-btn:hover {
    background: white;
    color: #333;
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Scroll indicator */
.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 3;
    animation: bounce 2s infinite;
}

.scroll-indicator a {
    color: white;
    font-size: 1.5rem;
    opacity: 0.8;
    transition: all 0.3s ease;
}

.scroll-indicator a:hover {
    opacity: 1;
    transform: scale(1.2);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0) translateX(-50%);
    }
    40% {
        transform: translateY(-20px) translateX(-50%);
    }
    60% {
        transform: translateY(-10px) translateX(-50%);
    }
}

/* About Section */
.about {
    background-color: transparent;
    position: relative;
    overflow: hidden;
}

.about::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(106, 17, 203, 0.05) 0%, rgba(37, 117, 252, 0.05) 100%);
    border-radius: 50%;
    z-index: 0;
}

/* Animation classes for scroll reveal */
.fade-in-up {
    opacity: 0;
    transform: translateY(50px);
    transition: opacity 0.8s ease, transform 0.8s ease;
}

.fade-in-up.appear {
    opacity: 1;
    transform: translateY(0);
}

.fade-in-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: opacity 0.8s ease, transform 0.8s ease;
    transition-delay: 0.2s;
}

.fade-in-left.appear {
    opacity: 1;
    transform: translateX(0);
}

.fade-in-right {
    opacity: 0;
    transform: translateX(50px);
    transition: opacity 0.8s ease, transform 0.8s ease;
    transition-delay: 0.4s;
}

.fade-in-right.appear {
    opacity: 1;
    transform: translateX(0);
}

/* Additional animations */
.animate-item {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.5s ease;
}

.animate-item.appear {
    opacity: 1;
    transform: translateY(0);
}

.animate-form-item {
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.5s ease;
}

.animate-form-item.appear {
    opacity: 1;
    transform: translateX(0);
}

/* Contact item animations with delays */
.contact-item:nth-child(1).appear {
    transition-delay: 0.3s;
}

.contact-item:nth-child(2).appear {
    transition-delay: 0.5s;
}

.contact-item:nth-child(3).appear {
    transition-delay: 0.7s;
}

/* Form item animations with delays */
.form-row:nth-child(1) .animate-form-item:nth-child(1).appear {
    transition-delay: 0.3s;
}

.form-row:nth-child(1) .animate-form-item:nth-child(2).appear {
    transition-delay: 0.4s;
}

.form-group:nth-child(3).appear {
    transition-delay: 0.5s;
}

.form-group:nth-child(4).appear {
    transition-delay: 0.6s;
}

/* Pulse animation for button */
.pulse-animation {
    animation: pulse 2s infinite;
}

#contact-form .primary-btn {
    display: block;
    margin: 0 auto;
    margin-top: 40px;
    padding: 15px 40px;
    font-size: 1.1rem;
}

#contact-form .primary-btn i {
    margin-right: 8px;
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(30, 136, 229, 0.6);
    }
    70% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(30, 136, 229, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(30, 136, 229, 0);
    }
}

.about-content {
    display: flex;
    align-items: center;
    gap: 50px;
    padding: 0 10%;
    position: relative;
    z-index: 1;
}

.about-image {
    flex: 0.8;
}

.about-image img {
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-width: 80%;
    margin: 0 auto;
    display: block;
}

.about-text {
    flex: 1.2;
}

.about-text p {
    margin-bottom: 30px;
    color: #b0b0b0;
    font-size: 1.1rem;
}

.personal-info {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.info-item {
    display: flex;
    flex-direction: column;
}

.info-item span:first-child {
    font-weight: 600;
    color: #e0e0e0;
}

.info-item span:last-child {
    color: #b0b0b0;
}

/* Education Section */
.education {
    background-color: transparent;
    position: relative;
    overflow: hidden;
}

.education::after {
    content: '';
    position: absolute;
    bottom: -30%;
    left: -30%;
    width: 80%;
    height: 80%;
    background: radial-gradient(circle, rgba(106, 17, 203, 0.05) 0%, rgba(37, 117, 252, 0.05) 100%);
    border-radius: 50%;
    z-index: 0;
}

.education-content {
    padding: 0 10%;
    position: relative;
    z-index: 1;
}

.education-item {
    display: flex;
    align-items: center;
    gap: 50px;
    margin-bottom: 50px;
    background-color: rgba(30, 30, 30, 0.8);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.education-info {
    flex: 2;
}

.education-info h3 {
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: #e0e0e0;
}

.education-info p {
    color: #b0b0b0;
}

.education-image {
    flex: 1;
}

.education-image img {
    border-radius: 10px;
}

/* Skills Section */
.skills {
    background-color: transparent;
    position: relative;
    overflow: hidden;
    padding-bottom: 80px;
}

.skills::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(30, 136, 229, 0.05) 0%, rgba(100, 181, 246, 0.05) 100%);
    z-index: 0;
}

.skills-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 5%;
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.skills-category {
    background-color: rgba(30, 30, 30, 0.8);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.skills-category:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.skills-category-title {
    display: flex;
    align-items: center;
    margin-bottom: 25px;
    gap: 15px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 15px;
}

.skills-category-title i {
    font-size: 1.8rem;
    color: #1e88e5;
    background: rgba(30, 136, 229, 0.1);
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    border: 1px solid rgba(30, 136, 229, 0.3);
}

.skills-category-title h3 {
    font-size: 1.5rem;
    color: #e0e0e0;
    font-weight: 600;
    margin: 0;
}

.skills-list {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: flex-start;
}

.skill-item {
    background: linear-gradient(135deg, rgba(30, 136, 229, 0.15) 0%, rgba(100, 181, 246, 0.15) 100%);
    border: 1px solid rgba(30, 136, 229, 0.3);
    border-radius: 25px;
    padding: 12px 20px;
    margin: 0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 15px rgba(30, 136, 229, 0.1);
}

.skill-item:hover {
    transform: translateY(-3px) scale(1.05);
    background: linear-gradient(135deg, rgba(30, 136, 229, 0.25) 0%, rgba(100, 181, 246, 0.25) 100%);
    border-color: rgba(30, 136, 229, 0.5);
    box-shadow: 0 8px 25px rgba(30, 136, 229, 0.2);
}

.skill-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.skill-item:hover::before {
    left: 100%;
}

.skill-item span {
    color: #e0e0e0;
    font-weight: 600;
    font-size: 0.95rem;
    position: relative;
    z-index: 1;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
}

/* All skill items use consistent blue theme */

/* Projects Section */
.projects {
    background-color: transparent;
    position: relative;
    overflow: hidden;
}



.projects::before {
    content: '';
    position: absolute;
    top: 10%;
    left: -10%;
    width: 60%;
    height: 60%;
    background: radial-gradient(circle, rgba(106, 17, 203, 0.05) 0%, rgba(37, 117, 252, 0.05) 100%);
    border-radius: 50%;
    z-index: 0;
}

.projects::after {
    content: '';
    position: absolute;
    bottom: 10%;
    right: -10%;
    width: 60%;
    height: 60%;
    background: radial-gradient(circle, rgba(37, 117, 252, 0.05) 0%, rgba(106, 17, 203, 0.05) 100%);
    border-radius: 50%;
    z-index: 0;
}

.projects-content {
    padding: 0 10%;
    position: relative;
    z-index: 1;
}

.project-item {
    display: flex;
    align-items: center;
    gap: 30px;
    margin-bottom: 50px;
    background-color: rgba(30, 30, 30, 0.8);
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.project-number {
    position: absolute;
    top: -20px;
    left: 30px;
    font-size: 2.5rem;
    font-weight: 700;
    color: #f0f0f0;
    z-index: 1;
}

.project-info {
    flex: 2;
    z-index: 2;
}

.project-title {
    margin-bottom: 15px;
}

.project-title h3 {
    font-size: 1.6rem;
    margin-bottom: 10px;
    color: #e0e0e0;
    line-height: 1.3;
}

.project-tech-stack {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 5px;
}

.tech-tag {
    display: inline-block;
    padding: 4px 10px;
    background-color: rgba(30, 136, 229, 0.15);
    color: #64b5f6;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    border: 1px solid rgba(30, 136, 229, 0.3);
}

.project-info p {
    color: #b0b0b0;
    margin-bottom: 20px;
    line-height: 1.6;
}

.project-features {
    margin-bottom: 25px;
    background-color: rgba(40, 40, 40, 0.5);
    border-radius: 8px;
    padding: 15px 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.project-features h4 {
    color: #e0e0e0;
    font-size: 1.1rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.project-features ul {
    list-style-type: none;
    padding-left: 5px;
}

.project-features li {
    color: #b0b0b0;
    margin-bottom: 8px;
    position: relative;
    padding-left: 20px;
}

.project-features li:before {
    content: '\f00c';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    left: 0;
    color: #1e88e5;
    font-size: 0.8rem;
}

.project-links {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
    justify-content: center;
}

.project-link {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    color: #ffffff;
    font-weight: 600;
    transition: all 0.3s ease;
    padding: 12px 25px;
    border-radius: 30px;
    background: linear-gradient(to right, #1e88e5, #64b5f6);
    border: none;
    box-shadow: 0 4px 15px rgba(30, 136, 229, 0.3);
    margin-top: 10px;
}

.project-link:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(30, 136, 229, 0.4);
    background: linear-gradient(to right, #1976d2, #1e88e5);
}

.project-link.disabled {
    background: linear-gradient(to right, #455a64, #607d8b);
    cursor: not-allowed;
    opacity: 0.8;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.project-link.disabled:hover {
    transform: translateY(0);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    background: linear-gradient(to right, #455a64, #607d8b);
}

.project-image {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.project-image img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.project-item:hover .project-image img {
    transform: scale(1.03);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
}

/* Gallery Section */
.gallery {
    background-color: transparent;
    position: relative;
    overflow: hidden;
}

.gallery::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(106, 17, 203, 0.03) 0%, rgba(37, 117, 252, 0.03) 100%);
    z-index: 0;
}

.gallery-content {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding: 0 10%;
    position: relative;
    z-index: 1;
}

.gallery-item {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.gallery-item img {
    transition: all 0.3s ease;
}

.gallery-item:hover img {
    transform: scale(1.05);
}

/* Contact Section */
.contact {
    background-color: transparent;
    padding: 120px 0;
    position: relative;
    overflow: hidden;
}

.contact::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20"><circle cx="10" cy="10" r="1" fill="%236a11cb" fill-opacity="0.1"/></svg>');
    z-index: 0;
}

.contact-container {
    display: flex;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    gap: 50px;
    position: relative;
    z-index: 1;
}

.contact-info {
    flex: 1;
    padding-right: 30px;
}

.left-aligned {
    text-align: left;
    margin-bottom: 30px;
}

.left-aligned h2 {
    font-size: 2.8rem;
    padding-bottom: 15px;
    display: inline-block;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    animation: typing 3s steps(30, end) 0.5s 1 normal both;
}

@keyframes typing {
    from { width: 0 }
    to { width: 100% }
}

.left-aligned h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 70%;
    height: 3px;
    background: linear-gradient(to right, #6a11cb, #2575fc);
}

.contact-description {
    font-size: 1.1rem;
    color: #666;
    margin-bottom: 40px;
    line-height: 1.8;
}

.contact-details {
    margin-bottom: 40px;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 25px;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(to right, #6a11cb, #2575fc);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;
}

.contact-icon::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, #6a11cb, #2575fc);
    border-radius: 50%;
    top: 0;
    left: 0;
    opacity: 0.7;
    animation: ripple 2s infinite;
}

@keyframes ripple {
    0% {
        transform: scale(1);
        opacity: 0.7;
    }
    100% {
        transform: scale(1.5);
        opacity: 0;
    }
}

.contact-icon i {
    color: white;
    font-size: 1.2rem;
}

.contact-text h3 {
    font-size: 1.2rem;
    margin-bottom: 5px;
    color: #333;
}

.contact-text p {
    color: #666;
    font-size: 1rem;
}

.contact-social h3 {
    font-size: 1.3rem;
    margin-bottom: 20px;
    color: #333;
}

.social-icons {
    display: flex;
    gap: 15px;
    animation: fadeInUp 1s ease-out 1s forwards;
    opacity: 0;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.social-icon {
    width: 40px;
    height: 40px;
    background-color: #f0f0f0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    transition: all 0.3s ease;
}

.social-icon:hover {
    background: linear-gradient(to right, #6a11cb, #2575fc);
    color: white;
    transform: translateY(-3px);
}

.contact-form-container {
    flex: 1;
    background-color: rgba(30, 30, 30, 0.9); /* Dark background with high opacity */
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    position: relative;
    border: 2px solid rgba(30, 136, 229, 0.3); /* Blue border */
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

.contact-form-container h3 {
    font-size: 1.8rem;
    margin-bottom: 30px;
    color: #1e88e5; /* Blue color */
    text-align: center;
    font-weight: 600;
    position: relative;
    padding-bottom: 15px;
}

.contact-form-container h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(to right, #1e88e5, #64b5f6);
    border-radius: 3px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
}

.form-group {
    margin-bottom: 25px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #e0e0e0;
    font-size: 1.05rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: #1e88e5;
    outline: none;
    box-shadow: 0 0 15px rgba(30, 136, 229, 0.2);
    background-color: #ffffff;
    animation: highlight 1s ease;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid rgba(30, 136, 229, 0.3); /* Blue border */
    border-radius: 8px;
    font-family: 'Poppins', sans-serif;
    font-size: 1rem;
    transition: all 0.3s ease;
    background-color: rgba(40, 40, 40, 0.9); /* Dark background with high opacity */
    color: #e0e0e0; /* Light text color */
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.form-group input:focus,
.form-group textarea:focus {
    border-color: #1e88e5; /* Stronger Blue */
    outline: none;
    box-shadow: 0 0 15px rgba(30, 136, 229, 0.3);
    background-color: rgba(50, 50, 50, 0.95); /* Slightly lighter dark background */
    animation: highlight 1s ease;
}

@keyframes highlight {
    0% {
        background-color: rgba(30, 136, 229, 0.2);
    }
    100% {
        background-color: rgba(50, 50, 50, 0.95); /* Slightly lighter dark background */
    }
}

.form-group textarea {
    height: 150px;
    resize: none;
}

/* Footer */
footer {
    background: transparent;
    color: #e0e0e0;
    padding: 70px 0 20px;
    position: relative;
    overflow: hidden;
    border-top: 1px solid rgba(30, 136, 229, 0.3);
}

.footer-content {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    gap: 40px;
}

.footer-left {
    display: flex;
    align-items: center;
}

.footer-branding {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}

.footer-name {
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    white-space: nowrap;
}

.footer-name span {
    color: #1e88e5;
}

.footer-tagline {
    color: #b0b0b0;
    font-size: 0.9rem;
    margin: 5px 0 0 0;
    white-space: nowrap;
}

.footer-section {
    flex: 1;
    padding: 0 25px;
    min-width: 180px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.footer-code-snippet {
    background-color: rgba(30, 30, 30, 0.8);
    border-radius: 10px;
    padding: 15px;
    border: 1px solid rgba(30, 136, 229, 0.3);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    flex: 1;
    max-width: 250px;
    margin: 0 40px;
}

.footer-code-snippet::before {
    content: '// Life of a Developer';
    position: absolute;
    top: 5px;
    right: 10px;
    color: #64b5f6;
    font-size: 0.8rem;
    opacity: 0.7;
}

.footer-code-snippet pre {
    margin: 0;
    font-family: 'Consolas', 'Monaco', monospace;
    font-size: 0.9rem;
    color: #e0e0e0;
    line-height: 1.5;
}

.footer-code-snippet code {
    color: #b0b0b0;
}

.code-keyword {
    color: #ff79c6; /* Pink */
    font-weight: bold;
}

.code-variable {
    color: #8be9fd; /* Cyan */
    font-style: italic;
}

.code-function {
    color: #50fa7b; /* Green */
}

.footer-code-snippet:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

.footer-section h4 {
    font-size: 1.2rem;
    margin-bottom: 15px;
    color: #e0e0e0;
    position: relative;
    display: inline-block;
    padding-bottom: 10px;
}

.footer-section h4::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background: linear-gradient(to right, #1e88e5, #64b5f6);
    border-radius: 2px;
}

.footer-links {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-top: 5px;
    list-style: none;
    padding: 0;
    text-align: center;
}

.footer-links li a {
    color: #b0b0b0;
    transition: all 0.3s ease;
    font-size: 0.95rem;
}

.footer-links li a:hover {
    color: #1e88e5;
}

.social-icons {
    display: flex;
    gap: 15px;
    margin-top: 10px;
}

.social-icons a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    background: rgba(30, 136, 229, 0.1);
    color: #1e88e5;
    border-radius: 50%;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    border: 1px solid rgba(30, 136, 229, 0.2);
}

.social-icons a:hover {
    background: linear-gradient(to right, #1e88e5, #64b5f6);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(30, 136, 229, 0.2);
}

.footer-bottom {
    text-align: center;
    padding-top: 40px;
    margin-top: 40px;
    border-top: 1px solid rgba(30, 136, 229, 0.1);
}

.footer-bottom p {
    font-size: 0.9rem;
    color: #b0b0b0;
}

/* Responsive Design */
@media screen and (max-width: 1024px) {
    .hero-text h1 {
        font-size: 3.5rem;
    }

    .hero-text h2 {
        font-size: 1.8rem;
    }

    .hero-overlay {
        padding: 0 8%;
    }

    .hero-text {
        padding-bottom: 12%;
    }

    .about-content,
    .skills-content {
        flex-direction: column;
    }

    .education-item {
        flex-direction: column;
    }

    .skills-list {
        justify-content: center;
    }

    .skill-item {
        font-size: 0.9rem;
        padding: 10px 16px;
    }
}

@media screen and (max-width: 768px) {
    header {
        padding: 15px 5%;
    }

    .menu-toggle {
        display: block;
    }

    nav {
        position: absolute;
        top: 100%;
        left: 0;
        width: 100%;
        background-color: rgba(25, 25, 25, 0.95);
        padding: 20px 0;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
        clip-path: polygon(0 0, 100% 0, 100% 0, 0 0);
        transition: all 0.3s ease;
        z-index: 100;
    }

    nav.active {
        clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
    }

    nav ul {
        flex-direction: column;
        align-items: center;
    }

    nav ul li {
        margin: 15px 0;
    }

    nav ul li a {
        color: #e0e0e0;
        font-size: 1.1rem;
    }

    .hero-overlay {
        align-items: center;
        justify-content: center;
        text-align: center;
        padding: 0 5%;
    }

    .hero-text {
        padding-bottom: 0;
        padding-top: 80px; /* Account for header */
    }

    .hero-buttons {
        justify-content: center;
    }

    .scroll-indicator {
        bottom: 20px;
    }

    .about-content,
    .education-content,
    .skills-content,
    .projects-content,
    .gallery-content {
        padding: 0 5%;
    }

    .project-item {
        flex-direction: column;
        gap: 30px;
    }

    .project-image {
        order: -1;
        margin-top: 20px;
    }

    .project-number {
        top: -15px;
        left: 20px;
        font-size: 2rem;
    }

    .project-links {
        justify-content: center;
    }

    .gallery-content {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    .contact-container {
        flex-direction: column;
        gap: 50px;
    }

    .contact-info {
        padding-right: 0;
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }

    section {
        padding: 70px 0;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
        gap: 30px;
    }

    .footer-left {
        justify-content: center;
    }

    .footer-branding {
        align-items: center;
    }

    .footer-section {
        width: 100%;
        padding: 0;
    }

    .footer-code-snippet {
        max-width: 100%;
        margin: 20px 0;
    }

    .footer-section h4::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .social-icons {
        justify-content: center;
    }
}

@media screen and (max-width: 480px) {
    .hero-text h1 {
        font-size: 2.5rem;
    }

    .hero-text h2 {
        font-size: 1.3rem;
        margin-bottom: 20px;
    }

    .hero-buttons {
        flex-direction: column;
        gap: 15px;
    }

    .primary-btn, .secondary-btn {
        padding: 10px 25px;
        font-size: 0.8rem;
    }

    .section-title h2 {
        font-size: 2rem;
    }

    .personal-info {
        grid-template-columns: 1fr;
    }
}